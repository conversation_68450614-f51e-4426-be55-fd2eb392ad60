import { getGuestDatelist, editUserName } from '../../api/yanqing'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    guestData:[],
    nickname: '',

    showModal: false,
    currentRemark: '',
    user_key: null,
    currentIndex: -1,

    // search
    searchValue:'',
    filteredData: [],

    // loadmore
    listData:[],
    pageIndex: 0,       // 改为从0开始，更符合编程习惯
    pageSize: 10,
    isLoading: false,
    noMoreData: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.gotListData();
  },

  onSearchInput: function(e) {
    this.setData({
      searchValue: e.detail.value
    });
  },

  onSearchConfirm: function() {
    this.filterData();
  },

  onClearSearch: function() {
    this.setData({
      searchValue: '',
      filteredData: this.data.guestData
    });
  },

  // onPageTap: function(e) {
  //   // 检查点击事件是否发生在搜索框内部
  //   const isInsideSearchBox = e.target.dataset.insideSearchBox;
  //   if (!isInsideSearchBox) {
  //     // 如果点击的是搜索框外部区域，执行搜索
  //     this.filterData();
  //   }
  // },

  filterData: function() {
    const searchText = this.data.searchValue.trim().toLowerCase();
    if (!searchText) {
      this.setData({
        filteredData: this.data.guestData
      });
      return;
    }

    const filtered = this.data.guestData.filter(item => {
      // 检查昵称是否匹配
      const nicknameMatch = item.nickname.toLowerCase().includes(searchText);
      
      // 检查标签是否匹配
      const tagMatch = item.tags.some(tag => 
        tag.toLowerCase().includes(searchText)
      );
      
      return nicknameMatch || tagMatch;
    });

    this.setData({
      filteredData: filtered
    });
  },

  // 显示自定义弹出层
  editGuestName(e) {
    this.clearInput();
    const index = e.currentTarget.dataset.index;
    const item = this.data.guestData[index];    
    console.log('弹出层guestData：',this.data.guestData);
    this.setData({
      showModal: true,
      currentRemark: item.nickname || '',
      currentIndex: index
    },() => {
      console.log('currentRemark:', this.data.currentRemark);
    });
  },

  // 隐藏自定义弹出层
  handleCancel() {
    console.log("cancel");
    this.setData({
      showModal: false
    });
  },  

  // 阻止事件冒泡
  stopPropagation() {
    return;
  },

  // 输入框内容变化
  handleInputChange(e) {
    this.setData({
      nickname: e.detail.value
    });
  },

  // 清空输入框
  clearInput() {
    this.setData({
      nickname: ''
    });
  },

  // 提交备注
  submitRemark() {
    const { nickname, currentRemark, currentIndex, guestData,filteredData } = this.data;
    if(!nickname || nickname.trim() === '') {
      wx.showToast({
        title: '备注不能为空！',
        icon: 'none'
      });
      return;
    }

    const newListData = [...filteredData];
    newListData[currentIndex].nickname = nickname;
    
    this.setData({
      user_key:currentIndex+1,
      filteredData: newListData,
      showModal: false
    },() => {
      console.log('更改姓名后的filteredData：',this.data.filteredData)
      console.log('newListData：',newListData)
    });
    
    // 调用API保存修改
    this.saveRemarkToAPI();   
    
  },

  // 提交修改
  saveRemarkToAPI() {
    wx.showLoading({
      title: '保存中...',
    });
    const params = {
      alias: this.data.nickname,
      user_key: JSON.stringify(this.data.user_key)
    }
    console.log('params:',params)
    editUserName(params)
    .then(res => {
      if(res && res.code === 200){        
        setTimeout(() => {
          wx.hideLoading();
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
        }, 1000);   
        setTimeout(() => {
          this.gotListData();
        }, 1000);
      }
    })
    .catch(error =>{
      console.log('保存失败原因：',error)
    })        
  },

  // pagesize loading
  gotListData(refresh = false) {
    // wx.showLoading({
    //   title: '数据加载中...',
    // });

    getGuestDatelist()
    .then(res =>{
      if(res && res.data && res.code === 200){
        // wx.hideLoading();

        if (this.data.isLoading || this.data.noMoreData) return;    
        this.setData({ isLoading: true });
        
        // 计算正确的页码
        const nextPage = refresh ? 0 : this.data.pageIndex;
        const start = nextPage * this.data.pageSize;
        const end = start + this.data.pageSize;
        
        console.log(`加载数据 页码: ${nextPage} 范围: ${start}-${end}`);
        
        setTimeout(() => {
          const guestData = res.data;
          console.log('guestData',guestData)
          const newData = guestData.slice(start, end);
          const hasMore = end < guestData.length;
          console.log('newData',newData)
          
          this.setData({
            guestData:guestData,
            filteredData: refresh ? newData : [...this.data.filteredData, ...newData],
            pageIndex: nextPage + 1,  // 确保页码递增
            isLoading: false,
            noMoreData: !hasMore
          },() =>{
            // 重置默认数据
            // this.setData({
            //   filteredData: guestData
            // })
            // console.log('filteredData',this.data.filteredData)
          });
          
          console.log(`加载完成 当前页码: ${this.data.pageIndex} 是否有更多: ${hasMore}`);
        }, 800);
      }
    })
    .catch(error =>{
      console.log('打印失败原因：',error)
    })    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ noMoreData: false });
    this.gotListData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.gotListData();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})