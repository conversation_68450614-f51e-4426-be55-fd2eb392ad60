import { addCommentData } from '../../api/yanqing'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    id:'1',
    ratingValue:'5',
    serviceTips:['服务','菜品质量','餐厅环境'],
    selectedTags: [true, false, false], // 对应标签的选中状态
    hasSelection: true, // 是否有选中项
    areaValue:'',
    maxlength:'300',
    isSubmitEnabled: false // 是否启用提交按钮
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      selectedTags: [this.data.serviceTips[0]]
    });

    // 接收宴请ID参数
    if (options.banquetId) {
      this.setData({
        banquetId: options.banquetId
      });
      console.log('接收到宴请ID:', options.banquetId);
    }
  },

  // 输入评论
  inpAreaValue(e){
    const inpValue = e.detail.value;
    this.setData({
      areaValue: inpValue
    })
    this.checkSubmitEnabled()
  },

  // 评分
  ratingOnChange(event) {
    console.log('当前值：' + event.detail);
    this.setData({
      ratingValue: event.detail,
    });
    this.checkSubmitEnabled()
  },

  // 检查是否可以提交
  checkSubmitEnabled() {
    const enabled = this.data.areaValue.trim().length > 0;
    // console.log(enabled)
    this.setData({
      isSubmitEnabled: enabled
    });
  },

  // 切换标签选中状态
  toggleTag: function(e) {
    const index = e.currentTarget.dataset.index;
    let selectedTags = this.data.selectedTags;
    
    // 切换选中状态
    selectedTags[index] = !selectedTags[index];
    
    // 检查是否有至少一个选中项
    const hasSelection = selectedTags.some(item => item);
    
    this.setData({
      selectedTags: selectedTags,
      hasSelection: hasSelection
    });
  },
  

  addBanquetComment(){
    if (!this.data.hasSelection) {
      wx.showToast({
        title: '请选择分享体验',
        icon: 'none'
      });
      return;
    }

    // 获取选中的标签文本
    const selectedItems = this.data.serviceTips.filter(
      (item, index) => this.data.selectedTags[index]
    );
    
    const params = {
      id:this.data.banquetId,
      score:this.data.ratingValue,
      tags:selectedItems,
      comment:this.data.areaValue
    }        
    addCommentData(params).then(res =>{
      wx.showToast({
        title: '提交中',
        icon:'loading'
      })
      if(res && res.code === 200){
        wx.hideLoading()      
        wx.showToast({
          title: '提交成功',
          icon:'success'
        })
        setTimeout(function() {
          wx.navigateTo({
            url: '/subpackages/banquet/pages/banquet-invitation/banquet-invitation',
          })
        },800)
        
      }
    }).catch(error =>{
      wx.showToast({
        title: '提交失败',
        icon:'error'
      })
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})