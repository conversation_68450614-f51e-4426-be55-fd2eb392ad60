<view class="banquet-guestdoc">
  <!-- search -->
  <view class="whitebox banquet-searchbox">
    <view class="search-input-container">
      <view class="search-icon">
        <image src="/image/groupmeal/ss.png" mode="aspectFit" />
      </view>
      <input class="search-input" placeholder="搜索姓名、标签" value="{{searchValue}}" bindinput="onSearchInput" bindconfirm="onSearchConfirm" placeholder-style="color: #999;" />
      <view class="clear-btn" wx:if="{{searchValue}}" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- list -->
  <view class="banquet-guestlist" bindscrolltolower="loadMore" lower-threshold="100">
    <view class="guestlist-pad">
      <!-- has data -->
      <view class="whitebox guestlist-item mt20" wx:for="{{filteredData}}" wx:key="index" data-index="{{index}}" wx:if="{{filteredData.length > 0}}">
        <view class="guestlist-ttcon flex">
          <view class="guestlist-tit f30 sgray bold">{{item.nickname}}</view>
          <view class="guestlist-tool flex">
            <view class="guestlist-tool-item flex" bindtap="editGuestName" data-index="{{index}}">
              <image src="/image/groupmeal/edit.png" mode="aspectFit" />
            </view>
            <view class="guestlist-tool-item moreicon flex" bindtap="moreEditTool" data-index="{{index}}">
              <image src="/image/groupmeal/tool.png" mode="aspectFit" />
            </view>
          </view>
        </view>
        <view class="guestlist-tags flex mt15"><text class="f24 sgray" wx:for="{{item.tags}}" wx:key="index">{{item}}</text></view>
      </view>
    </view>

    <!-- no data -->
    <view class="guest-no-data" wx:if="{{filteredData.length === 0 && !isLoading}}">
      <image src="/image/groupmeal/nodata.png" mode="aspectFit"/>
      <view class="nodata-text f24 qgray mt10">未匹配到数据</view>
    </view>

    <!-- no data -->
    <view class="guest-no-data guestload" wx:if="{{isLoading}}">
      <view class="nodata-text f24 qgray mt10">加载中...</view>
    </view>

    
    <!-- no data -->
    <view class="guest-no-data guestload" wx:if="{{noMoreData && filteredData.length > 10}}">
      <view class="nodata-text f24 qgray mt10">—— 已经到底啦 ——</view>
    </view>


  </view>
</view>

<!-- layer -->
<view class="custom-toast-mask" wx:if="{{showModal}}">
  <view class="custom-toast-container" catchtap="stopPropagation">
    <view class="toast-header">
      <text>备注</text>
    </view>
    
    <view class="toast-body">
      <view class="input-wrapper">
        <input 
          class="input-field" 
          placeholder="请输入备注姓名" 
          value="{{nickname}}" 
          bindinput="handleInputChange"
        />
        <view class="clear-btn" wx:if="{{nickname}}" bindtap="clearInput">×</view>
      </view>
    </view>
    
    <view class="toast-footer">
      <button class="footer-btn cancel" bindtap="handleCancel">取消</button>
      <button class="footer-btn confirm" bindtap="submitRemark">确认</button>
    </view>
  </view>
</view>