// import { AnimatorControllerStateModel } from 'XrFrame/kanata/lib/index';
import { restaurantDataList, getPositionRegeo, collectDataApi } from '../../api/yanqing'
Page({
  

  /**
   * 页面的初始数据
   */
  data: {
    is_collect:0,  // 点赞
    restaurant_key:'',
    positionTitext:'未授权',
    isFilterStadus:false, //是否显示筛选层
    mapPosition:false, //是否开启定位
    ratingstarList:['⭐ 5星','⭐ 4星以上','⭐ 3星以上'],
    mealDataList:['中餐','西餐','日料','法餐','素食','烧烤','面点','海鲜','粤菜','川菜','湘菜','意菜'],   
    selectedRatings: [],
    selectedMeals: [],
    lnglat:[], //保存经度和纬度
    topTabCon:['我的收藏','查看榜单'],
    TabType:'2',
    currentTab: 1,
    mixPrice:[],
    mixDistance:[],
    collect_num: 0,
    noDataStadus:false,

    // loading
    allData: [],          // 所有获取到的数据
    pageSize: 10,         // 每页加载数量
    pageIndex: 0,       // 当前页码
    noMoreData: false,        // 是否还有更多数据
    isLoading: false,       // 是否正在加载

    // 模拟数据
    resListData:[],

    //slider
    priceRange: {
      min: 0,
      max: 1000
    }, 
    distanceRange: {
      min: 0,
      max: 500
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.checkUserAgreePositon();
  },

  // 我的收藏和查看榜单切换
  topTabClick(e){
    const index = e.currentTarget.dataset.index,
          type = index + 1;              
    this.setData({
      currentTab: index,
      TabType:type,
    });
    console.log('type类型:',this.data.TabType);
    this.ConfirmCanFilter(this.data.TabType);
  },

  // 获取定位
  getLocalPositon(){
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        const {latitude,longitude} = res; // latitude:纬度，范围为 -90~90，负数表示南纬。longitude：经度，范围为 -180~180，负数表示西经
        this.setData({
          mapPosition:false,
          lnglat:[longitude,latitude]
        })        
        console.log("用户已授权，并且获取经纬度：",this.data.lnglat);
        const lnglatLen = this.data.lnglat;
        if(lnglatLen && lnglatLen.length > 0){
          this.getLocalAdressName();
          this.ConfirmCanFilter();
        }
      }
    })
  },

  // 通过纬度和经度获取定位名称
  getLocalAdressName(){
    wx.showLoading({
      title: '获取定位中',
    })
    const params = {
      lnglat: this.data.lnglat.join(',') 
    };
    getPositionRegeo(params)
    .then(res => {
      console.log("接口响应:", res);
      if (res && res.data.address) {
        wx.hideLoading();
        this.setData({
          positionTitext: res.data.address
        });
      } else {
        console.error('返回数据缺少 address 字段:', res);
      }
    })
    .catch(error => {
      console.error('获取定位名称失败:', error);
      this.setData({
        positionTitext: '获取位置信息失败'
      });
    });
  },

  // 检查用户是否已授权
  checkUserAgreePositon(){    
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.userLocation']) {
          wx.authorize({
            scope: 'scope.userLocation',
            success: () => {
              this.getLocalPositon();
            },
            fail: () => {            
              console.log("用户拒绝授权")
              this.setData({
                mapPosition:true,
                positionTitext:'未授权'
              })
            }
          })
        } else {
          // 直接获取位置
          this.getLocalPositon();     
        }
      }

    })
  },

  // 用户未开启定位，重新授权定位
  reGetLocaPosition(){
    wx.openSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          // 用户已授权
          this.setData({
            showAuthTip: false,
            locationAuth: true
          });
          this.getLocalPositon();
        }
      }
    });      
  },

  // 评分选择
  onRatingChange(e) {
    console.log('父组件接收:', e.detail.selected);
    this.setData({ 
      selectedRatings: e.detail.selected 
    }, () => {
      console.log('父组件更新完成:', this.data.selectedRatings);
    });
  },

  // 菜系选择
  onMealChange(e) {
    this.setData({
      selectedMeals: e.detail.selected
    })
    console.log('selectedMeals: ',this.data.selectedMeals)
  },
  
  // 价格变动
  onPriceSliderChange(e) {
    this.setData({
      priceRange: {
        min: e.detail.leftValue,
        max: e.detail.rightValue
      }
    })
    // console.log(this.data.priceRange.min+"====="+this.data.priceRange.max);
  },

  // 距离变动
  onDistanceSliderChange(e) {
    this.setData({
      distanceRange: {
        min: e.detail.leftValue,
        max: e.detail.rightValue
      }
    })
  },    

  // 重置数据
  resetSlider() {
    // 重置滑杆数据和位置
    this.selectComponent('#priceRange').handleReset();
    this.selectComponent('#distanceRange').handleReset();    

    // 重置评分&菜系
    const nonData = [];    
    this.setData({
      selectedRatings:nonData,
      selectedMeals:nonData,
    })
  },  

  // 点击展示餐厅过滤层
  resrecFilterData(){
    this.setData({
      isFilterStadus:true
    })
  },

  // 处理收藏人数和距离显示判断
  formatData(data) {
    return data.map(item => {
      // 格式化距离
      let formattedDistance;
      const distanceNum = parseInt(item.distance);
      if (distanceNum >= 1000) {
        formattedDistance = (distanceNum / 1000).toFixed(1) + 'km';
      } else {
        formattedDistance = distanceNum + 'm';
      }
      
      // 格式化收藏数
      let formattedCollectNum;
      const collectNum = parseInt(item.collect_num);
      if (collectNum > 500) {
        formattedCollectNum = '500+';
      } else {
        formattedCollectNum = collectNum.toString();
      }
      
      return {
        ...item,
        formattedDistance,  // 新增格式化后的距离字段
        formattedCollectNum // 新增格式化后的收藏数字段
      };
    });
  },

  // 提交应用筛选结果
  ConfirmCanFilter(type){    
    this.setData({
      isFilterStadus:false, //临时关闭，接口开通后删除代码。
      // allData: [],
      // resListData: [],
      // currentPage: 0,
      // hasMore: true
    })   
    const params = {
      type: this.data.TabType,
      lnglat: this.data.lnglat.join(','),
      price: [this.data.priceRange.min, this.data.priceRange.max].join(','),
      distance: [this.data.distanceRange.min * 1000, this.data.distanceRange.max * 1000].join(','),
      rating : this.data.selectedRatings.join(','),
      tags : this.data.selectedMeals.join(',')
    }
    console.log("提交的数据:", params);
    restaurantDataList(params).then(res =>{
      wx.showLoading({
        title: '数据加载中',
        icon:'loading'
      })
      if(res && res.code === 200 && res.data.length > 0){
        wx.hideLoading()  
        const resListData = this.formatData(res.data);
        this.setData({
          resListData:resListData,          
          isFilterStadus:false,          
          noDataStadus:false
        }, () => {
          wx.hideLoading();
          // 初始加载第一页数据
          this.loadMoreData(true);
        })   
      }else{
        wx.hideLoading()  
        this.setData({
          noDataStadus:true,
          hasMore: false
        })
      }
    }).catch(error => {
      wx.showToast({
        title: '提交失败',
        icon:'error'
      })
    })
  },

  // 点赞功能 submit -- is_collect: newStatus 
  selectFavStadus(e) {
    const index = e.currentTarget.dataset.index,
          restaurant_key = e.currentTarget.dataset.key,
          resListData = this.data.resListData,    
          oldStatus = resListData[index].is_collect,
          newStatus = oldStatus === 0 ? 1 : 0;
    resListData[index].is_collect = newStatus;
    this.setData({ 
      is_collect: newStatus,
      resListData
    });
    // console.log(this.data.is_collect);

    console.log(this.data.restaurant_key)

    const params = {
      is_collect: this.data.is_collect,
      restaurant_key: restaurant_key
    }
    collectDataApi(params).then(res =>{
      if(res && res.code === 200){
        console.log('点赞成功');
      }
    }).catch(error =>{
      console.log('点赞失败：',error)
    })
    
  },

  // 跳转到详情页面
  GotoResDetail(e){
    const restaurant_key = e.currentTarget.dataset.key;    
    wx.navigateTo({
      url: `/subpackages/banquet/pages/banquet-resdetail/banquet-resdetail?restaurant_key=${restaurant_key}`,
      success: (res) => {
			  console.log('跳转到搜索页面成功');
		  },
		  fail: (err) => {
			console.error('跳转失败:', err);
		  }
    })
  },

  // 加载更多数据
  loadMoreData(refresh = false) {
    if (this.data.isLoading || this.data.noMoreData) return;    
    this.setData({ isLoading: true });
    
    // 计算正确的页码
    const nextPage = refresh ? 0 : this.data.pageIndex;
    const start = nextPage * this.data.pageSize;
    const end = start + this.data.pageSize;    
    console.log(`加载数据 页码: ${nextPage} 范围: ${start}-${end}`);

    setTimeout(() => {
      const allData = this.data.resListData;
      const newData = allData.slice(start, end);
      const hasMore = end < allData.length;
      console.log('allData',allData)
      console.log('newData',newData)
      this.setData({
        allData:allData,
        resListData: refresh ? newData : [...this.data.allData, ...newData],
        pageIndex: nextPage + 1,  // 确保页码递增
        isLoading: false,
        noMoreData: !hasMore
      });      
      console.log(`加载完成 当前页码: ${this.data.pageIndex} 是否有更多: ${hasMore}`);
      console.log('noMoreData:',this.data.noMoreData)
    }, 800);
    
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {    
    this.setData({ noMoreData: false });
    this.loadMoreData(true);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    this.loadMoreData(false);
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})