page{
  height: 100vh;
  background: #F6F6F5;
}
.banquet-guestdoc{
  padding: 24rpx;
  height: 90vh;
}
.banquet-searchbox{
  padding: 24rpx 32rpx;
}

.search-input-container {
  width: 100%;
  height: 80rpx;
  background: #F1F2F4;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.search-icon image{
  width: 40rpx;
  height: 40rpx;
}

.search-input {
  flex: 1;
  height: 100%;
  border: none;
  background: transparent;
  font-size: 28rpx;
  color: #333;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}


/* list */
.banquet-guestlist{
  height: calc(100vh - 100rpx)
}
.guestlist-pad{
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
}
.guestlist-ttcon,.guestlist-tool{
  justify-content: space-between;
}
.guestlist-tool image{
  width: 32rpx;
  height: 32rpx;
}
.guestlist-tool-item{
  width: 45rpx;
  height: 45rpx;
  justify-content: center;
  align-items: center;
}
.guestlist-tool .moreicon{
  margin-left: 20rpx;
}
.guestlist-tags{
  justify-content: left;
}
.guestlist-tags text{
  display: block;
  margin-right:16rpx;
  line-height: 48rpx;
  background: #F4F4F6;
  border-radius: 8rpx;
  padding: 0 16rpx;
}

/* 弹出层 */
.custom-toast-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.custom-toast-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.toast-header {
  padding: 30rpx 30rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.toast-body {
  padding: 40rpx 30rpx 50rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  padding: 20rpx;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
}

.clear-btn {
  margin-left: 10rpx;
  width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 36rpx;
  color: #999;
}

.toast-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.footer-btn {
  flex: 1;
  border-radius: 0;
  border: none;
  background: none;
  font-size: 30rpx;
  padding: 10rpx 0;
}

.footer-btn::after {
  border: none;
}

.cancel {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.confirm {
  color: #79AA6B;
  font-weight: bold;
}
.guest-no-data{
  padding: 80rpx 0;
  text-align: center;
}
.guestload{
  padding:20rpx 0 70rpx;
}
.guest-no-data image{
  width: 100rpx;
  height: 100rpx;
}