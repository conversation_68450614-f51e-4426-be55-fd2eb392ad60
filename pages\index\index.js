// index.js
import { getBanner, getConstitutionCard, getDiet } from '@/api/home'
import { checkLoginOrRedirect, isUserLoggedIn } from '@utils/auth'
import { navigateTo } from '@utils/navigator'

// 合并所有配置常量
const CONFIG = {
  ANIMATION: {
    HIGHLIGHT_TIMEOUT: 1500,
    RIPPLE_TIMEOUT: 500
  },
  ELLIPSE: {
    centerX: 50,
    centerY: 50,
    radiusX: 35,
    radiusY: 25
  },
  TOUCH: {
    // 动态计算，不使用固定值
    centerX: 0,
    centerY: 0
  },
  SCROLL_SCALE: {
    DEBUG: false, // 设置为true启用调试信息
    MAX_SCALE: 1.5,
    MIN_SCALE: 0.6,
    INFLUENCE_RANGE: 1.3,
    // 渐变动画配置
    MAX_OPACITY: 1.0,
    MIN_OPACITY: 0.3,
    MAX_BLUR: 0,
    MIN_BLUR: 0.4
  },
  // 惯性滑动配置 - 简化版本，减少抖动
  INERTIA: {
    FRICTION: 0.94,           // 摩擦系数，适中的惯性效果
    MIN_VELOCITY: 0.3,        // 最小速度阈值
    VELOCITY_SCALE: 0.6,      // 速度缩放因子，适中的惯性强度
    MAX_VELOCITY: 25,         // 最大速度限制
    FRAME_RATE: 60            // 动画帧率
  }
}

const MENU_POSITIONS = [
  { index: 0, angle: 180, name: 'family', icon: '/image/home/<USER>', wuxingType: 'family' },
  { index: 1, angle: 240, name: 'group', icon: '/image/home/<USER>', wuxingType: 'fire' },
  { index: 2, angle: 300, name: 'banquet', icon: '/image/home/<USER>', wuxingType: 'earth' },
  { index: 3, angle: 0, name: 'travel', icon: '/image/home/<USER>', wuxingType: 'metal' },
  { index: 4, angle: 60, name: 'punk', icon: '/image/home/<USER>', wuxingType: 'water' },
  { index: 5, angle: 120, name: 'chronic', icon: '/image/home/<USER>', wuxingType: 'wood' }
]

// 菜单动作映射
const ACTION_MAP = {
  family: '家庭营养',
  group: '团体聚餐',
  banquet: '宴会定制',
  travel: '旅行饮食',
  punk: '养生朋克',
  chronic: '慢病调理'
}

// 导航路径配置
const NAVIGATION_PATHS = {
  // 主菜单路径
  family: '/subpackages/main/pages/family-nutrition/family-nutrition',
  family_plan: '/subpackages/main/pages/family-plan/family-plan',
  group: '/subpackages/canteen/pages/group-meal/group-meal',
  banquet: '/subpackages/banquet/pages/banquet-invitation/banquet-invitation',
  travel: '/subpackages/main/pages/data-entry/data-entry',
  punk: '/subpackages/main/pages/punk-health/punk-health',
  chronic: '/subpackages/main/pages/chronic-diet/chronic-diet',
  
  // 其他功能路径
  body_card: '/subpackages/main/pages/body-report/body-report',
  food_record: '/subpackages/main/pages/food/food',
  food_search: '/subpackages/main/pages/food-search/food-search',
  user_profile: '/subpackages/main/pages/user-profile/user-profile'
}

Page({
  data: {
    // API数据字段
    since_last: 22,
    solar_term: '芒种',
    temperature: 25,
    humidify: 80,
    constitution_type: '痰湿体',
    constitution_features: ['痰湿体', '易疲劳', '嗜睡多痰'],
    constitution_suggestions: '今天天气较为寒冷，应用温热食物\n驱散寒湿，多喝水增加脾胃运化...',
    constitution_tags: [],

    // 体质膳调数据
    dietData: null,
    nutritionData: {
      calorie: 0,
      maxCalorie: 0,
      protein: 0,
      maxProtein: 0,
      carb: 0,
      maxCarb: 0,
      fat: 0,
      maxFat: 0,
      caloriePercent: 0,
      proteinPercent: 0,
      carbPercent: 0,
      fatPercent: 0,
      diet_key: '',
    },
    familyMeals: {
      breakfast: [],
      lunch: [],
      dinner: []
    },

    // 数据加载状态
    isDataLoaded: false,

    // 餐次配置
    mealCategories: [
      {
        key: 'breakfast',
        title: '早餐',
        desc: '营养搭配',
        icon: '/image/home/<USER>'
      },
      {
        key: 'lunch',
        title: '午餐',
        desc: '均衡营养',
        icon: '/image/home/<USER>'
      },
      {
        key: 'dinner',
        title: '晚餐',
        desc: '清淡养生',
        icon: '/image/home/<USER>'
      }
    ],

    // 头部配置
    statusBarHeight: 20,
    headerHeight: 64,
    headerPaddingTop: 20,
    contentStyle: 'margin-top: 64px;',

    // 轮播图数据
    healthCards: [],

    // 环形菜单相关
    currentCircleIndex: -1,
    touchStartX: 0,
    touchStartY: 0,
    touchStartAngle: 0,
    isCircleRotating: false,
    menuItemPositions: MENU_POSITIONS,
    ellipseConfig: CONFIG.ELLIPSE,
    calculatedMenuPositions: [],
    selectionRippleIndex: -1,

    // 动画控制
    energyFlowActive: false,
    taijiRotationSpeed: 1,
    constellationActive: true,
    lifeCoreBreathing: true,
    currentWuxingFlow: 'wood',

    // 星系轨道系统
    galaxyRotation: {
      angle: 0,
      isUserDragging: false,
      velocity: 0,              // 当前旋转速度
      lastAngle: 0,             // 上一次的角度
      lastTimestamp: 0,         // 上一次的时间戳
      isInertiaActive: false,   // 惯性滑动是否激活
      velocityHistory: [],      // 速度历史记录，用于平滑计算
      initialAngle: 0,          // 触摸开始时的角度，用于累积计算
      touchCenterX: 0,          // 触摸中心点X
      touchCenterY: 0           // 触摸中心点Y
    },

    // 滚动缩放效果相关
    scrollTop: 0,
    functionNavRect: null,
    screenCenterY: 0
  },

  highlightTimer: null,
  inertiaAnimationId: null,  // 惯性动画ID

  // 通用工具方法
  showToast: (title, icon = 'none', duration = 2000) => {
    wx.showToast({ title, icon, duration })
  },

  // 计算营养成分百分比
  calculateNutritionPercentage(current, max) {
    if (!max || max <= 0) return 0
    return Math.min(Math.round((current / max) * 100), 100)
  },

  // 初始化方法
  initCircleMenuItems() {
    this.calculateMenuPositions()
    this.setData({
      currentCircleIndex: -1,
      calculatedMenuPositions: this.data.calculatedMenuPositions
    })
  },

  calculateMenuPositions() {
    const { ellipseConfig, menuItemPositions, galaxyRotation, scrollTop, functionNavRect, screenCenterY } = this.data

    const positions = menuItemPositions.map(item => {
      const totalAngle = item.angle + galaxyRotation.angle
      const angleRad = (totalAngle * Math.PI) / 180
      const cosAngle = Math.cos(angleRad)
      const sinAngle = Math.sin(angleRad)

      // 计算基础位置
      const baseX = ellipseConfig.centerX + ellipseConfig.radiusX * cosAngle
      const baseY = ellipseConfig.centerY + ellipseConfig.radiusY * sinAngle

      // 计算滚动缩放效果和渐变动画
      let scale = 1
      let opacity = 1
      let blur = 0

      if (functionNavRect && screenCenterY > 0) {
        // 计算图标在屏幕中的实际Y位置
        const iconAbsoluteY = functionNavRect.top + (baseY / 100) * functionNavRect.height
        const iconScreenY = iconAbsoluteY - scrollTop

        // 计算距离屏幕中心的距离
        const distanceFromCenter = Math.abs(iconScreenY - screenCenterY)

        // 计算缩放比例 (距离中心越近越大)
        const maxDistance = screenCenterY * CONFIG.SCROLL_SCALE.INFLUENCE_RANGE
        const normalizedDistance = Math.min(distanceFromCenter / maxDistance, 1)

        // 使用平滑的缩放曲线，增强大小渐变效果
        const scaleRange = CONFIG.SCROLL_SCALE.MAX_SCALE - CONFIG.SCROLL_SCALE.MIN_SCALE
        scale = CONFIG.SCROLL_SCALE.MAX_SCALE - (Math.pow(normalizedDistance, 0.7) * scaleRange)

        // 计算透明度 (距离中心越近越清晰)
        const opacityRange = CONFIG.SCROLL_SCALE.MAX_OPACITY - CONFIG.SCROLL_SCALE.MIN_OPACITY
        opacity = CONFIG.SCROLL_SCALE.MAX_OPACITY - (Math.pow(normalizedDistance, 0.6) * opacityRange)

        // 计算模糊度 (距离中心越近越清晰)
        const blurRange = CONFIG.SCROLL_SCALE.MIN_BLUR - CONFIG.SCROLL_SCALE.MAX_BLUR
        blur = CONFIG.SCROLL_SCALE.MAX_BLUR + (Math.pow(normalizedDistance, 0.7) * blurRange)

        // 确保值在合理范围内
        scale = Math.max(CONFIG.SCROLL_SCALE.MIN_SCALE, Math.min(CONFIG.SCROLL_SCALE.MAX_SCALE, scale))
        opacity = Math.max(CONFIG.SCROLL_SCALE.MIN_OPACITY, Math.min(CONFIG.SCROLL_SCALE.MAX_OPACITY, opacity))
        blur = Math.max(CONFIG.SCROLL_SCALE.MAX_BLUR, Math.min(CONFIG.SCROLL_SCALE.MIN_BLUR, blur))

        // 调试信息
        if (CONFIG.SCROLL_SCALE.DEBUG && item.index === 0) {
          console.log(`图标${item.index}: 屏幕Y=${iconScreenY.toFixed(1)}, 距离中心=${distanceFromCenter.toFixed(1)}, 缩放=${scale.toFixed(2)}, 透明度=${opacity.toFixed(2)}, 模糊=${blur.toFixed(1)}`)
        }
      }

      return {
        ...item,
        x: baseX,
        y: baseY,
        currentAngle: totalAngle,
        scale: scale,
        opacity: opacity,
        blur: blur
      }
    })

    this.setData({ calculatedMenuPositions: positions })
  },

  // 事件处理方法
  handleCircleItemClick(e) {
    const { index, type } = e.currentTarget.dataset
    const numericIndex = parseInt(index, 10)

    this.clearHighlightTimer()

    this.setData({
      currentCircleIndex: numericIndex,
      selectionRippleIndex: numericIndex
    })

    wx.vibrateShort({ type: 'light' })

    setTimeout(() => {
      this.setData({ selectionRippleIndex: -1 })
    }, CONFIG.ANIMATION.RIPPLE_TIMEOUT)

    this.highlightTimer = setTimeout(() => {
      this.setData({ currentCircleIndex: -1 })
    }, CONFIG.ANIMATION.HIGHLIGHT_TIMEOUT)

    this.executeMenuAction(type)
  },

  handleTouchStart(e) {
    if (e.touches.length !== 1) return

    const { clientX, clientY } = e.touches[0]

    // 动态计算触摸中心点
    this.getTouchCenter((centerX, centerY) => {
      const startAngle = Math.atan2(clientY - centerY, clientX - centerX) * 180 / Math.PI
      const currentTime = Date.now()

      // 平滑停止惯性动画，避免突然中断
      if (this.data.galaxyRotation.isInertiaActive) {
        this.stopInertiaAnimation()
        // 短暂延迟以确保状态同步
        setTimeout(() => {
          this.initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY)
        }, 16) // 一帧的时间
      } else {
        this.initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY)
      }
    })
  },

  getTouchCenter(callback) {
    const { functionNavRect } = this.data
    if (functionNavRect) {
      // 使用已有的区域信息
      const centerX = functionNavRect.left + functionNavRect.width / 2
      const centerY = functionNavRect.top + functionNavRect.height / 2
      callback(centerX, centerY)
    } else {
      // 重新获取区域信息
      const query = wx.createSelectorQuery().in(this)
      query.select('.function-nav').boundingClientRect((rect) => {
        if (rect) {
          this.setData({ functionNavRect: rect })
          const centerX = rect.left + rect.width / 2
          const centerY = rect.top + rect.height / 2
          callback(centerX, centerY)
        }
      }).exec()
    }
  },

  initTouchState(clientX, clientY, startAngle, currentTime, centerX, centerY) {
    this.setData({
      touchStartX: clientX,
      touchStartY: clientY,
      touchStartAngle: startAngle,
      'galaxyRotation.isUserDragging': true,
      'galaxyRotation.lastAngle': this.data.galaxyRotation.angle,
      'galaxyRotation.initialAngle': this.data.galaxyRotation.angle, // 记录触摸开始时的角度
      'galaxyRotation.lastTimestamp': currentTime,
      'galaxyRotation.velocity': 0,
      'galaxyRotation.isInertiaActive': false,
      'galaxyRotation.velocityHistory': [], // 清空速度历史
      'galaxyRotation.touchCenterX': centerX, // 保存触摸中心点
      'galaxyRotation.touchCenterY': centerY,
      isCircleRotating: true
    })

    // 暂停滚动缩放计算，避免冲突
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = null
    }
  },

  handleTouchMove(e) {
    const { isCircleRotating, galaxyRotation: { isUserDragging, lastTimestamp, touchCenterX, touchCenterY }, touchStartAngle } = this.data

    if (!isCircleRotating || e.touches.length !== 1 || !isUserDragging) return

    const { clientX, clientY } = e.touches[0]
    // 使用保存的触摸中心点
    const centerX = touchCenterX
    const centerY = touchCenterY
    const currentAngle = Math.atan2(clientY - centerY, clientX - centerX) * 180 / Math.PI
    const currentTime = Date.now()

    // 修复：计算从初始触摸点到当前位置的总角度变化
    let totalAngleDelta = currentAngle - touchStartAngle
    if (totalAngleDelta > 180) totalAngleDelta -= 360
    if (totalAngleDelta < -180) totalAngleDelta += 360

    // 计算新的绝对角度：初始角度 + 总的角度变化
    const initialAngle = this.data.galaxyRotation.initialAngle // 触摸开始时的角度
    const newAngle = ((initialAngle + totalAngleDelta) % 360 + 360) % 360

    // 计算速度（基于实际的角度变化）
    const timeDelta = currentTime - lastTimestamp
    let velocity = 0
    if (timeDelta > 0 && timeDelta < 100) {
      let angleChange = newAngle - this.data.galaxyRotation.angle
      if (angleChange > 180) angleChange -= 360
      if (angleChange < -180) angleChange += 360
      velocity = angleChange / timeDelta * 1000 // 转换为角度/秒
    }

    // 简化速度平滑 - 只保留最近3个值
    const velocityHistory = this.data.galaxyRotation.velocityHistory || []
    velocityHistory.push(velocity)
    if (velocityHistory.length > 3) {
      velocityHistory.shift()
    }

    // 简单的速度平滑
    let smoothVelocity = velocity
    if (velocityHistory.length >= 2) {
      smoothVelocity = velocityHistory.reduce((sum, v) => sum + v, 0) / velocityHistory.length
    }

    this.setData({
      'galaxyRotation.angle': newAngle,
      'galaxyRotation.lastTimestamp': currentTime,
      'galaxyRotation.velocity': smoothVelocity,
      'galaxyRotation.velocityHistory': velocityHistory
      // 注意：不再更新 touchStartAngle，保持初始触摸角度不变
    })

    // 直接计算位置
    this.calculateMenuPositions()
  },

  handleTouchEnd() {
    if (!this.data.isCircleRotating) return

    const { galaxyRotation: { velocity, velocityHistory } } = this.data

    this.setData({
      isCircleRotating: false,
      'galaxyRotation.isUserDragging': false
    })

    // 简化速度计算，使用最近的速度值
    let finalVelocity = velocity
    if (velocityHistory && velocityHistory.length > 1) {
      // 简单平均最近的速度值
      finalVelocity = velocityHistory.reduce((sum, v) => sum + v, 0) / velocityHistory.length
    }

    // 如果有足够的速度，启动惯性滑动
    const absVelocity = Math.abs(finalVelocity)
    if (absVelocity > CONFIG.INERTIA.MIN_VELOCITY) {
      this.startInertiaAnimation(finalVelocity)
    } else {
      // 恢复滚动缩放计算
      setTimeout(() => {
        this.calculateMenuPositions()
      }, 100)
    }
  },

  // 惯性滑动动画相关方法
  startInertiaAnimation(initialVelocity) {
    // 限制最大速度
    let velocity = Math.max(-CONFIG.INERTIA.MAX_VELOCITY, Math.min(CONFIG.INERTIA.MAX_VELOCITY, initialVelocity))
    velocity *= CONFIG.INERTIA.VELOCITY_SCALE

    this.setData({
      'galaxyRotation.velocity': velocity,
      'galaxyRotation.isInertiaActive': true
    })

    this.runInertiaAnimation()
  },

  runInertiaAnimation() {
    if (!this.data.galaxyRotation.isInertiaActive) return

    const { galaxyRotation: { velocity, angle } } = this.data

    // 检查速度是否足够小，如果是则停止动画
    if (Math.abs(velocity) < CONFIG.INERTIA.MIN_VELOCITY) {
      this.stopInertiaAnimation()
      return
    }

    // 计算新的角度和速度 - 简化处理
    const frameTime = 1000 / CONFIG.INERTIA.FRAME_RATE
    const newAngle = ((angle + velocity * frameTime / 1000) % 360 + 360) % 360
    const newVelocity = velocity * CONFIG.INERTIA.FRICTION

    this.setData({
      'galaxyRotation.angle': newAngle,
      'galaxyRotation.velocity': newVelocity
    })

    this.calculateMenuPositions()

    // 继续动画
    this.inertiaAnimationId = setTimeout(() => {
      this.runInertiaAnimation()
    }, frameTime)
  },

  stopInertiaAnimation() {
    if (this.inertiaAnimationId) {
      clearTimeout(this.inertiaAnimationId)
      this.inertiaAnimationId = null
    }

    this.setData({
      'galaxyRotation.isInertiaActive': false,
      'galaxyRotation.velocity': 0
    })

    // 恢复滚动缩放计算
    setTimeout(() => {
      this.calculateMenuPositions()
    }, 100)
  },

  // 用户信息相关
  bindViewTap: () => wx.navigateTo({ url: '../logs/logs' }),

  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    const { nickName } = this.data.userInfo

    this.setData({
      "userInfo.avatarUrl": avatarUrl,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },

  onInputChange(e) {
    const nickName = e.detail.value
    const { avatarUrl } = this.data.userInfo

    this.setData({
      "userInfo.nickName": nickName,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },

  getUserProfile() {
    wx.getUserProfile({
      desc: '展示用户信息',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },

  // 滚动缩放效果相关方法
  initScrollScaleEffect() {
    // 获取屏幕信息
    const systemInfo = wx.getSystemInfoSync()
    const screenCenterY = systemInfo.windowHeight / 2

    this.setData({ screenCenterY })

    // 延迟获取功能导航区域的位置信息，确保DOM已渲染
    setTimeout(() => {
      this.getFunctionNavRect()
    }, 500)
  },

  getFunctionNavRect() {
    const query = wx.createSelectorQuery().in(this)
    query.select('.function-nav').boundingClientRect((rect) => {
      if (rect) {
        this.setData({ functionNavRect: rect })
        console.log('功能导航区域位置信息:', rect)
        // 初始计算一次位置
        this.calculateMenuPositions()
      }
    }).exec()
  },

  onPageScroll(e) {
    const { scrollTop } = e
    this.setData({ scrollTop })

    // 节流处理，避免频繁计算
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
    }

    this.scrollTimer = setTimeout(() => {
      // 重新计算菜单位置和缩放
      this.calculateMenuPositions()
    }, 16) // 约60fps
  },

  // 生命周期
  onLoad() {
    console.log('页面加载开始')
    this.getSystemInfo()
    this.initCircleMenuItems()
    this.initTraditionalAnimations()
    this.initScrollScaleEffect() // 初始化滚动缩放效果
    this.loadBannerData()
    this.loadConstitutionData()
    this.loadDietData() // 加载体质膳调数据

    // 标记数据已加载
    this.setData({ isDataLoaded: true })
    console.log('页面初始化完成')
  },

  getSystemInfo() {
    try {
      const { statusBarHeight } = wx.getSystemInfoSync()
      const headerHeight = statusBarHeight + 44

      this.setData({
        statusBarHeight,
        headerHeight,
        headerPaddingTop: statusBarHeight,
        contentStyle: `margin-top: ${headerHeight}px;`
      })

      console.log('系统信息获取成功:', { statusBarHeight, headerHeight })
    } catch (e) {
      console.error('获取系统信息失败:', e)
      this.setData({
        statusBarHeight: 20,
        headerHeight: 64,
        headerPaddingTop: 20,
        contentStyle: 'margin-top: 64px;'
      })
    }
  },

  // 数据加载方法
  async loadBannerData() {
    try {
      const response = await getBanner()
      console.log('Banner数据获取成功:', response)

      if (response?.code === 200 && response?.data && Array.isArray(response.data)) {
        const bannerCards = response.data.map((item, index) => ({
          id: `banner_${index}`,
          image: item.img_url,
          jumpPage: item.jump_page
        }))
        this.setData({ healthCards: bannerCards })
      }
    } catch (error) {
      console.error('Banner数据获取失败:', error)
      if (error?.code !== 403 && error?.statusCode !== 403) {
        // 非403错误才处理，403由全局处理
      }
    }
  },

  async loadConstitutionData() {
    try {
      const response = await getConstitutionCard()
      console.log('体质卡片数据获取成功:', response)

      if (response?.code === 200 && response?.data) {
        const { data: constitutionData } = response

        // 直接使用扩展运算符简化数据设置
        this.setData({ ...constitutionData })

        console.log('体质数据更新完成:', constitutionData)
      }
    } catch (error) {
      console.error('体质卡片数据获取失败:', error)
      if (error?.code !== 403 && error?.statusCode !== 403) {
        this.showToast('体质数据加载失败')
      }
    }
  },

  // 调用体质膳调模块
  async loadDietData() {
    try {
      wx.showLoading({ title: '获取膳调数据...' })

      const response = await getDiet()
      console.log('体质膳调数据获取成功:', response)

      if (response?.code === 200 && response?.data) {
        const { data: dietData } = response

        // 处理营养数据
        const nutritionData = {
          calorie: dietData.calorie || 0,
          maxCalorie: dietData.max_calorie || 0,
          protein: dietData.protein || 0,
          maxProtein: dietData.max_protein || 0,
          carb: dietData.carb || 0,
          maxCarb: dietData.max_carb || 0,
          fat: dietData.fat || 0,
          maxFat: dietData.max_fat || 0,
          // 预计算百分比
          caloriePercent: this.calculateNutritionPercentage(dietData.calorie, dietData.max_calorie),
          proteinPercent: this.calculateNutritionPercentage(dietData.protein, dietData.max_protein),
          carbPercent: this.calculateNutritionPercentage(dietData.carb, dietData.max_carb),
          fatPercent: this.calculateNutritionPercentage(dietData.fat, dietData.max_fat),
          diet_key: dietData.diet_key || '',
        }

        // 调试信息：检查diet_key是否正确获取
        console.log('API返回的dietData:', dietData)
        console.log('处理后的nutritionData.diet_key:', nutritionData.diet_key)

        // 处理家庭餐食数据
        const familyMeals = this.processFamilyMealsData(dietData.family || [])

        // 更新膳调相关数据
        this.setData({
          dietData: dietData,
          nutritionData: nutritionData,
          familyMeals: familyMeals
        })

        console.log('体质膳调数据更新完成:', dietData)
        this.showToast('膳调数据加载成功', 'success')
      }
    } catch (error) {
      console.error('体质膳调数据获取失败:', error)
      if (error?.code !== 403 && error?.statusCode !== 403) {
        this.showToast('膳调数据加载失败')
      }
    } finally {
      wx.hideLoading()
    }
  },

  // 处理家庭餐食数据
  processFamilyMealsData(familyData) {
    const meals = {
      breakfast: [],
      lunch: [],
      dinner: []
    }

    if (!Array.isArray(familyData)) {
      console.warn('家庭餐食数据格式错误:', familyData)
      return meals
    }

    familyData.forEach((mealGroup, index) => {
      if (!mealGroup || typeof mealGroup !== 'object') {
        console.warn(`餐食组${index}数据格式错误:`, mealGroup)
        return
      }

      // 处理早餐
      if (Array.isArray(mealGroup.breakfast)) {
        meals.breakfast = meals.breakfast.concat(mealGroup.breakfast)
      }

      // 处理午餐
      if (Array.isArray(mealGroup.lunch)) {
        meals.lunch = meals.lunch.concat(mealGroup.lunch)
      }

      // 处理晚餐
      if (Array.isArray(mealGroup.dinner)) {
        meals.dinner = meals.dinner.concat(mealGroup.dinner)
      }
    })

    console.log('处理后的餐食数据:', {
      breakfast: meals.breakfast.length,
      lunch: meals.lunch.length,
      dinner: meals.dinner.length,
      detail: meals
    })

    return meals
  },

  // 手动刷新体质膳调数据
  async refreshDietData() {
    if (checkLoginOrRedirect()) {
      await this.loadDietData()
    }
  },

  // 检查登录状态并重新加载数据
  checkAndReloadData() {
    // 如果用户已登录，重新加载需要登录的数据
    if (isUserLoggedIn()) {
      console.log('用户已登录，重新加载数据')

      // 重新加载需要登录状态的数据
      this.loadBannerData()
      this.loadConstitutionData()
      this.loadDietData()

      // 标记数据已加载
      this.setData({ isDataLoaded: true })
    } else {
      console.log('用户未登录，跳过数据加载')
      // 重置数据加载状态
      this.setData({ isDataLoaded: false })
    }
  },

  // ============================
  // 统一页面跳转逻辑
  // ============================
  
  /**
   * 统一的页面跳转方法
   * @param {string} pathKey - 路径配置中的键名
   * @param {Object} params - 跳转参数对象
   */
  navigate(pathKey, params = {}) {
    const path = NAVIGATION_PATHS[pathKey];
    if (!path) {
      console.error('未找到路径配置:', pathKey);
      return;
    }
    
    // 检查登录状态
    if (!checkLoginOrRedirect()) return;
    
    // 构建查询参数字符串
    const queryParams = Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    // 完整的导航路径
    const fullPath = queryParams ? `${path}?${queryParams}` : path;
    
    // 执行跳转
    wx.navigateTo({
      url: fullPath,
      fail: (error) => {
        console.error(`跳转到 ${pathKey} 失败:`, error);
        this.showToast('页面跳转失败');
      }
    });
  },
  
  // ============================
  // 菜单跳转相关方法
  // ============================
  
  /**
   * 执行菜单动作
   * @param {string} type - 菜单类型
   * @param {string} params - 附加参数
   */
  executeMenuAction(type, params) {
    console.log('执行菜单动作:', type, '参数:', params);
    this.showActionFeedback(type);
    
    // 根据类型执行不同的跳转
    switch(type) {
      case 'family':
        // 家庭营养 - 带diet_key跳转到详情页，否则跳转到计划页
        if (params) {
          this.navigate('family', { dietKey: params });
        } else {
          this.navigate('family_plan');
        }
        break;
      case 'group':
        // 团体聚餐
        this.navigate('group');
        break;
      case 'banquet':
        // 宴会定制
        this.navigate('banquet');
        break;
      case 'travel':
        // 旅行饮食
        this.navigate('travel');
        break;
      case 'punk':
        // 养生朋克
        this.navigate('punk');
        break;
      case 'chronic':
        // 慢病调理
        this.navigate('chronic');
        break;
      default:
        console.warn('未知的菜单类型:', type);
    }
  },
  
  /**
   * 显示操作反馈提示
   * @param {string} type - 菜单类型
   */
  showActionFeedback(type) {
    const actionName = ACTION_MAP[type] || '未知功能';
    console.log(`正在打开: ${actionName}`);
    
    wx.showToast({
      title: `正在打开${actionName}`,
      icon: 'loading',
      duration: 1000
    });
  },
  
  // ============================
  // 页面事件处理跳转
  // ============================
  
  /**
   * 跳转到膳食计划
   * @param {Object} e - 事件对象
   */
  onDietPlanTap(e) {
    const { dietKey } = e.currentTarget.dataset;
    console.log('跳转到膳食计划:', dietKey);
    
    if (dietKey) {
      this.navigate('family', { dietKey });
    }
  },
  
  /**
   * 体质卡点击跳转
   */
  onBodyCardTap() {
    this.navigate('body_card');
  },
  
  /**
   * 记录餐次点击处理
   * @param {Object} e - 事件对象
   */
  onMealRecordTap(e) {
    const { mealType } = e.currentTarget.dataset;
    console.log('记录餐次:', mealType);
    
    this.navigate('food_record', { mealType });
  },
  
  /**
   * 食物项目点击处理
   * @param {Object} e - 事件对象
   */
  onFoodItemTap(e) {
    const { foodData, mealType } = e.currentTarget.dataset;
    console.log('点击食物:', foodData, '餐次:', mealType);
    
    this.navigate('food_record', { 
      foodName: foodData.dish_name, 
      mealType, 
      preselected: true 
    });
  },
  
  /**
   * 搜索按钮点击
   */
  onSearchTap() {
    this.navigate('food_search');
  },
  
  /**
   * 用户资料点击
   */
  onUserProfileTap() {
    this.navigate('user_profile');
  },
  
  /**
   * 体质膳调卡片点击事件
   */
  onDietCardTap() {
    this.refreshDietData();
  },

  /**
   * 轮播图点击事件
   * @param {Object} e - 事件对象
   */
  onBannerTap(e) {
    const { index } = e.currentTarget.dataset;
    const bannerItem = this.data.healthCards[index];

    if (!bannerItem || !bannerItem.jumpPage) {
      console.warn('轮播图跳转页面信息不完整:', bannerItem);
      return;
    }

    console.log('轮播图点击跳转:', bannerItem.jumpPage);

    // 检查登录状态
    if (!checkLoginOrRedirect()) return;

    // 执行跳转
    wx.navigateTo({
      url: `/${bannerItem.jumpPage}`,
      fail: (error) => {
        console.error('轮播图跳转失败:', error);
        this.showToast('页面跳转失败');
      }
    });
  },

  onHeaderInit(e) {
    const { statusBarHeight, headerHeight } = e.detail

    this.setData({
      statusBarHeight,
      headerHeight,
      headerPaddingTop: statusBarHeight,
      contentStyle: `margin-top: ${headerHeight}px;`
    })

    console.log('头部初始化完成:', { statusBarHeight, headerHeight })
  },

  // 动画系统
  initTraditionalAnimations() {
    this.setData({
      taijiRotationSpeed: 1,
      constellationActive: true,
      currentWuxingFlow: 'wood',
      energyFlowActive: true,
      lifeCoreBreathing: true
    })
  },

  activateWuxingEnergy(wuxingType = 'wood') {
    this.setData({
      currentWuxingFlow: wuxingType,
      energyFlowActive: true
    })
  },

  // 生命周期方法
  onUnload: () => this.clearAllTimers(),

  onHide() {
    console.log('页面隐藏')
    this.clearAllTimers()
    this.setData({
      currentCircleIndex: -1,
      selectionRippleIndex: -1
    })
  },

  onShow() {
    console.log('页面显示')

    // 设置tabbar选中状态
    this.getTabBar?.()?.setData?.({ selected: 0 })

    // 恢复动画状态
    this.setData({
      constellationActive: true,
      lifeCoreBreathing: true
    })

    // 重新获取功能导航区域位置信息
    setTimeout(() => {
      this.getFunctionNavRect()
    }, 300)

    // 检查登录状态，如果已登录则重新加载数据
    this.checkAndReloadData()
  },

  // 清理方法
  clearHighlightTimer() {
    if (this.highlightTimer) {
      clearTimeout(this.highlightTimer)
      this.highlightTimer = null
    }
  },

  clearAllTimers() {
    this.clearHighlightTimer()
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer)
      this.scrollTimer = null
    }
    // 清理惯性动画
    this.stopInertiaAnimation()
  },

  temporarilyDisableAnimations() {
    this.setData({
      energyFlowActive: false,
      constellationActive: false,
      lifeCoreBreathing: false
    })

    setTimeout(() => {
      this.setData({
        energyFlowActive: true,
        constellationActive: true,
        lifeCoreBreathing: true
      })
    }, 3000)
  }
})