---
type: "manual"
description: "原生微信小程序 · ES6/ESM · 强组件化 · 统一网络层 · 统一风格。用于约束在本仓库的生成/重构。"
---
# 微信小程序 · 项目规则

> 所有 AI 生成/改写均须遵守：**原生小程序、ES6+、组件化、服务层统一、可直接运行**。

## 0) 输出约束
- **一次性输出完整文件**：页面/组件必须同时给出 `.js/.wxml/.wxss/.json`，且**逐一标注文件路径**。
- **文件顺序**：同路径按 **JS → WXML → WXSS → JSON** 输出；禁止只给片段。
- **补齐依赖**：若引用到 `config/*`、`utils/*`、`services/*` 不存在文件，**同时生成最小实现**。

## 1) 语言与环境
- 仅使用**原生微信小程序 API**；禁止引入 React/Vue/Taro/uni-app。
- **ES6+**：`const/let`、箭头函数、解构/展开、模板字符串。
- **ES Modules**：只用 `import/export`，禁止 `require/module.exports`。
- 异步优先 **Promise/async‑await**；避免回调地狱。

## 2) 目录结构
- 新功能**优先抽成组件**放 `components/`；页面负责业务编排。

## 3) 命名与风格
- 目录/文件/样式类：**kebab-case**（`user-card`、`.main-title`）
- 变量/函数：**camelCase**；组件标签：**PascalCase**（`<UserCard />`）
- 代码风格：**2 空格、单引号、行末分号、行宽 100、尾随逗号 es5、文件末尾换行**。

## 4) 页面（Page）
- 只做**业务编排与数据聚合**；UI 交由组件。
- 生命周期顺序：`onLoad → onShow → onReady → 事件 → 其他 → onHide → onUnload`。
- 仅用 `this.setData()` 更新**UI 必需的最小片段**；**合并**多次更新；在 `onUnload` 清理定时器/监听。

## 5) 组件（Component）
- 必备字段：`properties`（对外）/`data`（内部）/`methods`（对外）。
- `options: { multipleSlots: true, styleIsolation: 'isolated' }`；必要时 `externalClasses`。
- 事件命名：`onTapXxx/onChangeXxx/onSubmitXxx`；对外事件 `this.triggerEvent(name, detail)`。
- **WXML 不写业务逻辑**，复杂计算放 JS（确需时用 WXS）。

## 6) 样式与资源
- 尺寸统一 **rpx**（无特殊需求不使用 px）。
- 主题/字号/间距等公共变量放 `app.wxss`；组件样式**局部且简洁**。
- 图片归档于 `assets/`，分子目录管理。

## 7) 数据与状态
- `data` 初值完整声明；避免动态引入未声明字段。
- 避免将**非 UI 必需的大对象**放入 `data`。
- 跨页面共享优先**参数传递**或 **App 实例**；默认引入状态库。

## 8) 网络与服务层（强制）
- **禁止**在页面/组件中直接 `wx.request`。
- 统一经 `utils/request.js`；业务接口写在 `api/*.js`，页面仅调用服务函数。

## 9) 兼容手机端（强制）
- 任何功能代码或者样式都必须兼容手机端，安卓和ios