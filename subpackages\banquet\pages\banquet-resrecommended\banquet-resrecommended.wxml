<view class="banquet-resrecommended">
  <view class="resrec-dingwei flex">
    <view class="resrec-dw-select f28">
      <van-icon name="location" class="f32" color="#79AA6B" />
      <text>{{positionTitext}}</text>
      <van-icon name="arrow-down" />
    </view>
    <view class="resrec-dw-shouc f28 {{currentTab === index ? 'liang' : ''}}" wx:for="{{topTabCon}}" wx:key="index" bindtap="topTabClick" data-index="{{index}}">{{item}}</view>
    <view class="resrec-dw-filter flex" bindtap="resrecFilterData"><van-icon name="filter-o" class="gray f34" /></view>
  </view>

  <!-- 未授权定位提示 -->
  <view class="whitebox rexrec-nopostion f24 flex mt20" wx:if="{{mapPosition}}" bindtap="reGetLocaPosition">
    <view class="resrec-nop-lef">
      <van-icon name="warning-o" class="waningicon" /> 未开启定位，按照所在城市进行推荐
    </view>
    <view class="resrec-nop-rig liang">授权定位</view>
  </view>

  <view class="resrec-datalist mt20" wx:if="{{!noDataStadus}}">
    <view class="resrec-pad">
      <view class="whitebox resrec-item flex" wx:for="{{resListData}}" wx:key="index" data-key="{{item.restaurant_key}}" data-index="{{index}}">
        <view class="flex" bindtap ="GotoResDetail" data-key="{{item.restaurant_key}}">
          <image src="{{item.photo}}" class="res-image" mode="aspectFit"/>
          <view class="restec-ct-info">
            <view class="restec-ct-tit f30 sgray bold">{{item.name}}</view>
            <view class="restec-ct-pos flex mt15">
              <view class="restec-ct-star flex"><text class="liang f26">{{item.rating}}</text> <image src="/image/groupmeal/xin-active.png" class="xin-image" mode="aspectFit"/></view>
              <view class="restec-ct-ms f24 qgray"><text>{{item.formattedCollectNum}}</text>人已收藏</view>
            </view>
            <view class="restec-ct-pos f24 qgray mt15"><text>{{item.formattedDistance}}</text></view>
            <view class="restec-ct-pos flex mt20">
              <text class="restec-ct-tags f22 liang" wx:for="{{item.tags}}" wx:key="index">{{item}}</text>
            </view>
          </view>
        </view>
        <!-- active:like -->
        <view class="restec-favourite">
          <van-icon name="like-o" class="likeicon" wx:if="{{item.is_collect === 0 ? true : false}}" size="26px" bindtap="selectFavStadus" data-index="{{index}}" data-key="{{item.restaurant_key}}" />
          <van-icon name="like" class="likeicon liang" wx:if="{{item.is_collect === 1 ? true : false}}" size="26px" bindtap="selectFavStadus" data-index="{{index}}" data-key="{{item.restaurant_key}}" />
        </view>
      </view>

      <!-- 加载中 -->
      <view class="load-more mt40 f24 qgray" wx:if="{{isLoading}}">
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 加载完毕 -->
      <view class="load-more mt40 f24 qgray" wx:if="{{noMoreData && resListData.length > 10}}">
        <view class="no-more-text">--- 已经到底啦 ---</view>
      </view>
    </view>

  </view>

  <!-- no data -->
  <view class="resrec-nodata-box flex" wx:if="{{resListData.length === 0 && !isLoading}}">
    <view class="resrec-nodata-con">
      <image src="/image/groupmeal/nodata.png" mode="aspectFit"/>
      <view class="resrec-nodata-text f24 qgray">暂无数据</view>
    </view>
  </view>

</view>

<cover-view class="filter-laybox" wx:if="{{isFilterStadus}}">
  <cover-view class="filter-laycon">
    <cover-view class="whitebox filter-content">
      <!-- Price filter -->
      <cover-view class="filter-item">
        <cover-view class="filter-tit f28 sgray bold flex">价格范围(元)：<cover-view class="filter-tit-text liang">{{priceRange.min}} - {{priceRange.max}}</cover-view></cover-view>
        <cover-view class="filter-count f26 flex mt25">
          <cover-view class="filter-num flex"><cover-view>0</cover-view>元</cover-view>
          <cover-view class="filter-num flex"><cover-view>1000</cover-view>元</cover-view>
        </cover-view>
        <cover-view class="slider-pubbox">
          <dual-slider 
            min="0"
            max="1000"
            minText="最低价格"
            maxText="最高价格"
            initialLeft="0"
            initialRight="1000"
            step="1"
            minGap="60" 
            id="priceRange"  
            rememberState="{{true}}"   
            storageKey="priceRange"     
            bind:change="onPriceSliderChange"
          />
        </cover-view>
        <cover-view class="filter-count filter-count-text f26 flex">
          <cover-view class="filter-num">最低</cover-view>
          <cover-view class="filter-num">最高</cover-view>
        </cover-view>
      </cover-view>

      <!-- Distance filter -->
      <cover-view class="filter-item mt80">
        <cover-view class="filter-tit f28 sgray bold flex">距离范围 (km)：<cover-view class="filter-tit-text liang">{{distanceRange.min}} - {{distanceRange.max}}</cover-view></cover-view>
        <cover-view class="filter-count f26 flex mt25">
          <cover-view class="filter-num flex"><cover-view>0</cover-view>km</cover-view>
          <cover-view class="filter-num flex"><cover-view>50</cover-view>km</cover-view>
        </cover-view>   
        <cover-view class="slider-pubbox"> 
          <dual-slider 
            min="0"
            max="50"
            minText="最近距离"
            maxText="最远距离"
            initialLeft="0"
            initialRight="50"
            step="1"
            minGap="5" 
            id="distanceRange" 
            rememberState="{{true}}"    
            storageKey="distanceRange"    
            bind:change="onDistanceSliderChange"
          />
        </cover-view>    
        <cover-view class="filter-count filter-count-text f26 flex">
          <cover-view class="filter-num">最近</cover-view>
          <cover-view class="filter-num">最远</cover-view>
        </cover-view>
      </cover-view>

      <!-- rating filter -->
      <cover-view class="filter-item mt80">
        <cover-view class="filter-tit f28 sgray bold">评分星级</cover-view>
        <cover-view class="filter-count filter-left rating-star-bg f26 flex mt20">          
          <tag-selector class="tagslectorfon" options="{{ratingstarList}}" selected="{{selectedRatings}}" bind:change="onRatingChange" />
          <!-- <cover-view class="filter-mum-item flex" wx:for="{{ratingstar}}" wx:key="index">
            <cover-image src="/image/groupmeal/xin-active.png" class="xin-image" mode="aspectFit"/> <cover-view class="gray f26">{{item}}</cover-view> 
          </cover-view>       -->
        </cover-view>
      </cover-view>

      <!-- Meal filter -->
      <cover-view class="filter-item mt80">
        <cover-view class="filter-tit f28 sgray bold">菜系</cover-view>
        <cover-view class="filter-meal f26 flex mt20">
          <tag-selector class="tagslectorfon" options="{{mealDataList}}" selected="{{selectedMeals}}" bind:change="onMealChange" />            
          <!-- <cover-view class="filter-meal-item gray flex" wx:for="{{mealDataList}}" wx:key="index">{{item}}</cover-view> -->
        </cover-view>
      </cover-view>

    </cover-view>
  </cover-view>

  <!-- bottom btn -->
  <cover-view class="ai-bottom-btn">
    <cover-view class="ai-bottom-pad">
      <button class="yygs-submit-btn f28 sgray bold" bindtap="resetSlider">重置</button>
      <button class="yygs-submit-btn submit-groupCan f28" bindtap="ConfirmCanFilter">应用筛选</button>
    </cover-view>
  </cover-view>

</cover-view>

