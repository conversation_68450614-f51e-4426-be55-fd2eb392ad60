page{
  height: 100vh;
  background: #F6F6F5;
}
.banquet-resrecommended{
  padding: 24rpx;
}
.resrec-dw-select text{
  padding: 0 5rpx;
}
.resrec-datalist{
  height: calc(100vh - 100rpx)
}
.resrec-pad{
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
}
.resrec-item{
  position: relative;
  padding: 20rpx;
}
.resrec-item .likeicon{
  position: absolute;
  top: 28rpx;
  right: 32rpx;
}
.res-image{
  width: 196rpx;
  height: 196rpx;
  border-radius: 16rpx;
}
.xin-image{
  margin-left: 5rpx;
  width: 32rpx;
  height: 32rpx;
}
.restec-ct-pos .restec-ct-tags{
  margin-right: 10rpx;
  display: block;
  line-height: 36rpx;
  padding: 0 15rpx;
  border-radius: 8rpx;
  background: #E8F3E5;
  box-shadow: 0rpx 16rpx 16rpx 0rpx rgba(0,0,0,0.01);
}
.restec-ct-info,.restec-ct-pos .restec-ct-ms{
  margin-left: 34rpx;
}
.restec-ct-tit{
  width: 320rpx;
  text-align: justify;
}
.resrec-dingwei{
  justify-content: space-between;
  padding:0 15rpx;
}
.resrec-dingwei .resrec-dw-filter{
  justify-content: space-between;
  align-items: center;
}
.resrec-dingwei .resrec-dw-filter:before{
  margin-right: 25rpx;
  height: 24rpx;
  width: 1px;
  background: #d4d4d4;
  content: "";
}

/* no dingwei */
.rexrec-nopostion{
  padding: 20rpx 20rpx;
  justify-content: space-between;
}
.rexrec-nopostion .resrec-nop-lef{
  color: #FD6301;
}
.rexrec-nopostion .resrec-nop-rig{
  padding-left: 32rpx;
  border-left: solid #d4d4d4 1px;
}

/* no data */
.resrec-nodata-box{
  height: 90vh;
  justify-content: center;
  align-items: center;
}
.resrec-nodata-con{
  flex: 1;
  text-align: center;
}
.resrec-nodata-con image{
  width: 150rpx;
  height: 150rpx;
}
.resrec-nodata-con .resrec-nodata-text{
  margin-top: 6rpx;
}

/* layer */
.filter-laybox{
  /* display: none; */
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background:#F6F6F5;
  z-index: 100;
}
.filter-laybox.showlayer{
  display: block;
}
.filter-laycon{
  padding: 24rpx;
}
.filter-item .filter-count{
  justify-content: space-between;
}
.filter-item .tagslectorfon{
  display: block;
  width: 100%;
}
.filter-item .filter-left{
  justify-content:left;
}
 .filter-item .filter-count-text{
  width: 638rpx;
}
/* .filter-item .filter-count-text .filter-num:last-child{
  position: relative;
  left: -15rpx;
} */
.filter-item .filter-slider{
  height: 140rpx;
  justify-content: space-between;  
  align-items: center;
}
.filter-item .filter-slfon{
  position: relative;
  /* margin: 0 auto; */
  left: 8rpx;
  width: 569rpx; 
}
.filter-mum-item{
  margin-right: 20rpx;
  height: 56rpx;
  line-height: 56rpx;
  padding: 0 20rpx 0 8rpx;
  background: #F4F4F6;
  border-radius: 8rpx;
}
.filter-mum-item .xin-image{
  margin-right: 8rpx;
}
.filter-meal{
  justify-content: space-between;
  flex-wrap: wrap;
}
.filter-meal .filter-meal-item{
  margin-bottom: 2%;
  width: 23.5%;
  height: 56rpx;
  line-height: 56rpx;
  background: #F4F4F6;
  border-radius: 8rpx;
  justify-content: center;
}

/* van slider */
.slider-pubbox{
  padding: 0;
}

.yusuan-progress-load{
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0rpx;
  width: 48rpx;
  height: 48rpx;
  top: 50%;
  margin-top:-26rpx;
  border-radius: 100%;
  background: #fff;  
  align-items: center;  border:solid #79AA6B 1px;
}
.yusuan-progress-load:before,.yusuan-progress-load:after{
  position: absolute;
  margin:-11rpx 4rpx 0;
  top: 50%;
  width: 2px;
  height: 22rpx;
  content: "";
  background:#d9d9d9;
}
.yusuan-progress-load:before{
  left: 12rpx;
}
.yusuan-progress-load:after{
  right: 12rpx;
}
.load-count{
  width: auto;
  position: relative;
  top: -58rpx;
}
.load-more{
  padding-bottom: 35rpx;
  text-align: center;
}

/* layer footer bottom */
.ai-bottom-btn{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  width: 100%;
  height: 170rpx;
  z-index: 100;
}
.ai-bottom-pad{
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 114rpx;
  box-sizing: border-box;
  height: 100%;
}
.ai-bottom-btn button{  
  width: 240rpx;
}
.ai-bottom-btn .submit-groupCan{
  border-color: #79AA6B;
  color: #fff;
  background: #79AA6B;
}